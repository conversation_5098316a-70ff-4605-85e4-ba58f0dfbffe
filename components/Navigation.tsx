'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { MessageSquare, Activity } from 'lucide-react';
import { cn } from '@/components/ui/utils';

export function Navigation() {
  const pathname = usePathname();
  const isTracing = pathname.startsWith('/tracing');
  
  // Determine the other page to navigate to
  const otherPage = isTracing 
    ? { name: 'Go to Chat', href: '/', icon: <MessageSquare className="h-4 w-4" /> }
    : { name: 'View Traces', href: '/tracing', icon: <Activity className="h-4 w-4" /> };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Link
        href={otherPage.href}
        className={cn(
          'flex items-center gap-2 px-4 py-2 rounded-full shadow-lg',
          'text-sm font-medium transition-colors',
          isTracing 
            ? 'bg-white text-purple-700 hover:bg-purple-50 border border-purple-200'
            : 'bg-white text-blue-700 hover:bg-blue-50 border border-blue-200'
        )}
      >
        {otherPage.icon}
        <span className="hidden sm:inline">{otherPage.name}</span>
      </Link>
    </div>
  );
}
