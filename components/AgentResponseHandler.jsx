import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const AgentResponseHandler = ({ message, onFormSubmit }) => {
	const [formData, setFormData] = useState({});
	const [formSpec, setFormSpec] = useState(null);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [submitResult, setSubmitResult] = useState(null);

	// Parse message and initialize form data
	useEffect(() => {
		try {
			let parsedMessage = message;

			// If the message is a string, try to parse it as JSON
			if (typeof parsedMessage === "string") {
				try {
					parsedMessage = JSON.parse(parsedMessage);
				} catch (e) {
					// Not a valid JSON string, so treat it as plain text
					setFormSpec(null);
					return;
				}
			}

			// Now, parsedMessage is either an object or the original plain text
			let formSpecData = null;

			if (
				parsedMessage &&
				typeof parsedMessage === "object" &&
				!Array.isArray(parsedMessage)
			) {
				// Handle new format: { response_type: 'form', response: { ... } }
				if (
					parsedMessage.response_type === "form" &&
					parsedMessage.response
				) {
					formSpecData = parsedMessage.response;
				}
				// Handle legacy format: { response_type: 'form_spec', form_spec: { ... } }
				else if (
					parsedMessage.response_type === "form_spec" &&
					parsedMessage.form_spec
				) {
					formSpecData = parsedMessage.form_spec;
				}
				// Handle direct form spec
				else if (parsedMessage.fields) {
					formSpecData = parsedMessage;
				}
			}

			// Check if we have a valid form spec
			if (
				formSpecData &&
				formSpecData.fields &&
				Array.isArray(formSpecData.fields)
			) {
				const initialData = {};
				formSpecData.fields.forEach((field) => {
					initialData[field.name] = field.value || "";
				});

				setFormData(initialData);
				setFormSpec(formSpecData);
			} else {
				// If no valid form spec is found, treat as plain text
				setFormSpec(null);
			}
		} catch (e) {
			console.error("Error parsing form spec:", e);
			setFormSpec(null);
		}
	}, [message]);

	const handleChange = (fieldName) => (event) => {
		const value = event.target?.value ?? event;
		setFormData((prev) => ({
			...prev,
			[fieldName]: value,
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setIsSubmitting(true);

		try {
			console.log("Form submitted with data:", formData);

			// Prepare the form data for submission
			const submissionData = {
				...formData,
				// Convert dates to ISO string format if they exist
				start_date: formData.start_date
					? new Date(formData.start_date).toISOString()
					: null,
				end_date: formData.end_date
					? new Date(formData.end_date).toISOString()
					: null,
			};

			// Call the provided onFormSubmit callback if available
			if (onFormSubmit) {
				const result = await onFormSubmit(submissionData);

				// Handle different response formats
				let resultMessage = "Form submitted successfully!";
				if (result && typeof result === "object") {
					if (result.function && result.function.arguments) {
						try {
							const args = JSON.parse(result.function.arguments);
							if (args.response_type === "success") {
								resultMessage = args.message || resultMessage;
							} else if (args.response_type === "error") {
								throw new Error(args.error || "An unknown error occurred");
							}
						} catch (e) {
							console.warn("Could not parse function response:", e);
						}
					}
				}

				setSubmitResult({
					success: true,
					message: resultMessage,
					data: submissionData,
				});
			} else {
				// For testing/demo purposes
				setSubmitResult({
					success: true,
					message: "Form submission simulated successfully!",
					data: submissionData,
				});
			}
		} catch (error) {
			console.error("Error submitting form:", error);
			setSubmitResult({
				success: false,
				message:
					error.message || "An error occurred while submitting the form.",
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	const renderField = (field) => {
		const fieldValue = formData[field.name] ?? "";

		switch (field.type) {
			case "date":
				return (
					<Input
						type="date"
						label={field.label}
						value={fieldValue || ""}
						onChange={handleChange(field.name)}
						required={field.required}
						className="w-full"
					/>
				);

			case "select":
				return (
					<div className="w-full">
						<label className="block text-sm font-medium text-gray-700 mb-1">
							{field.label}
						</label>
						<select
							value={fieldValue}
							onChange={handleChange(field.name)}
							required={field.required}
							className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
						>
							<option value="">Select {field.label}</option>
							{field.options?.map((option) => (
								<option key={option.value} value={option.value}>
									{option.label}
								</option>
							))}
						</select>
					</div>
				);

			case "number":
				return (
					<Input
						type="number"
						label={field.label}
						value={fieldValue}
						onChange={handleChange(field.name)}
						required={field.required}
						disabled={field.disabled}
						min={field.min}
						max={field.max}
						step={field.step || "any"}
						className="w-full"
					/>
				);

			case "text":
			default:
				return (
					<Input
						type={field.type || "text"}
						label={field.label}
						value={fieldValue}
						onChange={handleChange(field.name)}
						required={field.required}
						disabled={field.disabled}
						placeholder={field.placeholder}
						className="w-full"
					/>
				);
		}
	};

	if (!formSpec) {
		return (
			<div className="whitespace-pre-wrap break-words text-sm md:text-base">
				{typeof message === "string"
					? message
					: JSON.stringify(message, null, 2)}
			</div>
		);
	}

	return (
		<div className="my-2">
			{submitResult && (
				<div
					className={`mb-2 p-2 rounded-md ${
						submitResult.success
							? "bg-green-100 text-green-800"
							: "bg-red-100 text-red-800"
					}`}
				>
					{submitResult.message}
				</div>
			)}

			<Card className="mt-2">
				<CardContent className="p-4">
					<h3 className="text-lg font-semibold mb-2">
						{formSpec.title || "Update Room Prices"}
					</h3>
					{formSpec.description && (
						<p className="text-sm text-gray-600 mb-4">{formSpec.description}</p>
					)}

					<form onSubmit={handleSubmit} className="space-y-4">
						{formSpec.fields?.map((field) => (
							<div key={field.name} className="mb-4">
								{renderField(field)}
							</div>
						))}

						<div className="flex gap-2 pt-4">
							<Button
								type="submit"
								disabled={isSubmitting}
								className="bg-blue-600 hover:bg-blue-700"
							>
								{isSubmitting
									? "Submitting..."
									: formSpec.submit_button_text || "Submit"}
							</Button>
							<Button
								type="button"
								variant="outline"
								onClick={() => setSubmitResult(null)}
								disabled={isSubmitting}
							>
								Cancel
							</Button>
						</div>

						{isSubmitting && (
							<p className="text-sm text-gray-500 mt-2">Submitting...</p>
						)}
					</form>
				</CardContent>
			</Card>
		</div>
	);
};

export default AgentResponseHandler;
