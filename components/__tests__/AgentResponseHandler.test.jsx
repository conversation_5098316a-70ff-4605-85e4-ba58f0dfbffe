import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import <PERSON>R<PERSON>ponseHand<PERSON> from "../AgentResponseHandler";

const mockFormSpec = {
	response_type: "form_spec",
	form_spec: {
		title: "Update Room Price",
		description: "Update the price for a specific room type in a hotel.",
		fields: [
			{
				name: "hotel_name",
				label: "Hotel Name",
				type: "select",
				required: true,
				value: "",
				options: [
					{ value: "Taj Mahal Palace", label: "Taj Mahal Palace" },
					{ value: "Mountain View Lodge", label: "Mountain View Lodge" },
				],
			},
			{
				name: "room_type",
				label: "Room Type",
				type: "select",
				required: true,
				value: "",
				options: [
					{ value: "Deluxe King", label: "Deluxe King ($150.00)" },
					{
						value: "Presidential Suite",
						label: "Presidential Suite ($500.00)",
					},
				],
			},
			{
				name: "new_price",
				label: "New Price (per night)",
				type: "number",
				required: true,
				min: 0,
				step: 0.01,
			},
		],
		submit_method: "POST",
		submit_button_text: "Update Price",
	},
};

const mockOnFormSubmit = jest.fn();

describe("AgentResponseHandler", () => {
	beforeEach(() => {
		mockOnFormSubmit.mockClear();
	});

	it("renders form with correct fields when form spec is provided", () => {
		render(
			<AgentResponseHandler
				message={JSON.stringify(mockFormSpec)}
				onFormSubmit={mockOnFormSubmit}
			/>
		);

		expect(screen.getByText("Update Room Price")).toBeInTheDocument();
		expect(
			screen.getByText("Update the price for a specific room type in a hotel.")
		).toBeInTheDocument();
		expect(screen.getByLabelText("Hotel Name")).toBeInTheDocument();
		expect(screen.getByLabelText("Room Type")).toBeInTheDocument();
		expect(screen.getByLabelText("New Price (per night)")).toBeInTheDocument();
		expect(screen.getByText("Update Price")).toBeInTheDocument();
	});

	it("renders plain text when no form spec is provided", () => {
		const plainTextMessage = "This is a plain text message";
		render(
			<AgentResponseHandler
				message={plainTextMessage}
				onFormSubmit={mockOnFormSubmit}
			/>
		);

		expect(
			screen.getByText("This is a plain text message")
		).toBeInTheDocument();
	});

	it("handles form submission correctly", async () => {
		mockOnFormSubmit.mockResolvedValue({ success: true });

		render(
			<AgentResponseHandler
				message={JSON.stringify(mockFormSpec)}
				onFormSubmit={mockOnFormSubmit}
			/>
		);

		// Fill in the form
		fireEvent.change(screen.getByLabelText("Hotel Name"), {
			target: { value: "Taj Mahal Palace" },
		});
		fireEvent.change(screen.getByLabelText("Room Type"), {
			target: { value: "Deluxe King" },
		});
		fireEvent.change(screen.getByLabelText("New Price (per night)"), {
			target: { value: "200" },
		});

		// Submit the form
		fireEvent.click(screen.getByText("Update Price"));

		await waitFor(() => {
			expect(mockOnFormSubmit).toHaveBeenCalledWith({
				hotel_name: "Taj Mahal Palace",
				room_type: "Deluxe King",
				new_price: "200",
				start_date: null,
				end_date: null,
			});
		});
	});

	it("shows loading state during form submission", async () => {
		mockOnFormSubmit.mockImplementation(
			() => new Promise((resolve) => setTimeout(resolve, 100))
		);

		render(
			<AgentResponseHandler
				message={JSON.stringify(mockFormSpec)}
				onFormSubmit={mockOnFormSubmit}
			/>
		);

		// Fill in the form
		fireEvent.change(screen.getByLabelText("Hotel Name"), {
			target: { value: "Taj Mahal Palace" },
		});
		fireEvent.change(screen.getByLabelText("Room Type"), {
			target: { value: "Deluxe King" },
		});
		fireEvent.change(screen.getByLabelText("New Price (per night)"), {
			target: { value: "200" },
		});

		// Submit the form
		fireEvent.click(screen.getByText("Update Price"));

		// Check for loading state
		expect(screen.getByText("Submitting...")).toBeInTheDocument();
	});

	it("handles form submission errors", async () => {
		const errorMessage = "Failed to update price";
		mockOnFormSubmit.mockRejectedValue(new Error(errorMessage));

		render(
			<AgentResponseHandler
				message={JSON.stringify(mockFormSpec)}
				onFormSubmit={mockOnFormSubmit}
			/>
		);

		// Fill in the form
		fireEvent.change(screen.getByLabelText("Hotel Name"), {
			target: { value: "Taj Mahal Palace" },
		});
		fireEvent.change(screen.getByLabelText("Room Type"), {
			target: { value: "Deluxe King" },
		});
		fireEvent.change(screen.getByLabelText("New Price (per night)"), {
			target: { value: "200" },
		});

		// Submit the form
		fireEvent.click(screen.getByText("Update Price"));

		await waitFor(() => {
			expect(screen.getByText(errorMessage)).toBeInTheDocument();
		});
	});
});
