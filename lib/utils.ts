import { createBrowserClient } from '@supabase/ssr'
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function getSupabaseBrowserClient() {
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
  return supabase
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
