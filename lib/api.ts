// API utility functions for interacting with the backend
import { getSupabaseBrowserClient } from "./utils";
import { v4 as uuidv4 } from "uuid";
import { API_BASE_URL } from "../config/api";

interface ChatRequest {
	message: string;
	session_id?: string;
	agent_type?: string;
}

interface ChatResponse {
	response: string | Record<string, unknown>;
	session_id: string;
	agent_type: string;
	metadata?: Record<string, unknown>;
	response_type?: string;
}

interface BackendMessage {
	id: string;
	content: string;
	role: "user" | "assistant";
	created_at: string;
	metadata?: Record<string, unknown>;
}

/**
 * Sends a chat message to the backend
 * @param message The message content
 * @param sessionId Optional session ID for conversation continuity
 * @param agentType Optional agent type
 * @returns Promise with the chat response
 */
export const sendChatMessage = async (
	message: string,
	sessionId?: string,
	agentType: string = "hotel_manager"
): Promise<ChatResponse> => {
	const supabase = getSupabaseBrowserClient();
	const {
		data: { session },
	} = await supabase.auth.getSession();
	const payload: ChatRequest = {
		message,
		session_id: sessionId,
		agent_type: agentType,
	};

	try {
		const response = await fetch(`${API_BASE_URL}/chat`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				...(session?.access_token && {
					Authorization: `Bearer ${session.access_token}`,
				}),
			},
			body: JSON.stringify(payload),
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(errorData.detail || "Failed to send message");
		}

		return await response.json();
	} catch (error) {
		console.error("Error sending chat message:", error);
		throw error;
	}
};

/**
 * Sends a chat message with real-time status updates via Server-Sent Events
 * @param message The message content
 * @param sessionId Optional session ID for conversation continuity
 * @param agentType Optional agent type
 * @param onStatus Callback for status updates
 * @param onTextDelta Callback for streaming text deltas
 * @param onResponse Callback for the final response
 * @param onError Callback for errors
 * @returns Promise that resolves when the stream completes
 */
export const sendChatMessageStream = async (
	message: string,
	sessionId?: string,
	agentType: string = "hotel_manager",
	onStatus?: (status: string) => void,
	onTextDelta?: (delta: string) => void,
	onResponse?: (response: ChatResponse) => void,
	onError?: (error: string) => void
): Promise<void> => {
	const supabase = getSupabaseBrowserClient();
	const {
		data: { session },
	} = await supabase.auth.getSession();
	const payload: ChatRequest = {
		message,
		session_id: sessionId,
		agent_type: agentType,
	};

	try {
		const response = await fetch(`${API_BASE_URL}/chat/stream`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				...(session?.access_token && {
					Authorization: `Bearer ${session.access_token}`,
				}),
			},
			body: JSON.stringify(payload),
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(errorData.detail || "Failed to send message");
		}

		const reader = response.body?.getReader();
		if (!reader) {
			throw new Error("Failed to get response reader");
		}

		const decoder = new TextDecoder();
		let buffer = "";

		try {
			while (true) {
				const { done, value } = await reader.read();
				if (done) break;

				buffer += decoder.decode(value, { stream: true });
				const lines = buffer.split("\n");
				buffer = lines.pop() || ""; // Keep incomplete line in buffer

				for (const line of lines) {
					if (line.startsWith("data: ")) {
						try {
							const data = JSON.parse(line.slice(6));

							switch (data.type) {
								case "status":
									onStatus?.(data.message);
									break;
								case "text_delta":
									onTextDelta?.(data.delta);
									break;
								case "response":
									onResponse?.(data.data);
									break;
								case "error":
									onError?.(data.message);
									return;
								case "complete":
									return;
							}
						} catch (parseError) {
							console.warn("Failed to parse SSE data:", line);
						}
					}
				}
			}
		} finally {
			reader.releaseLock();
		}
	} catch (error) {
		console.error("Error in streaming chat:", error);
		onError?.(error instanceof Error ? error.message : "Unknown error");
		throw error;
	}
};

/**
 * Fetches chat history for a specific session
 * Note: The backend doesn't currently support fetching chat history,
 * so this is a placeholder that returns an empty array.
 * @param sessionId The session ID
 * @returns Promise with an empty array (placeholder)
 */
export const getChatHistory = async (
	_sessionId: string
): Promise<unknown[]> => {
	console.log("Chat history endpoint not implemented in backend");
	return [];
};

/**
 * Get conversation history for a session
 * @param sessionId The session ID to get history for
 * @returns Promise with the conversation history
 */
export const getSessionHistory = async (
	sessionId: string
): Promise<BackendMessage[]> => {
	const supabase = getSupabaseBrowserClient();
	const {
		data: { session },
	} = await supabase.auth.getSession();

	try {
		const response = await fetch(
			`${API_BASE_URL}/sessions/${sessionId}/messages`,
			{
				method: "GET",
				headers: {
					"Content-Type": "application/json",
					...(session?.access_token && {
						Authorization: `Bearer ${session.access_token}`,
					}),
				},
			}
		);

		if (!response.ok) {
			return [];
		}

		const data = await response.json();
		return data.messages || [];
	} catch (error) {
		console.error("Error getting session history:", error);
		return [];
	}
};

/**
 * Creates a new chat session
 * @returns Promise with the new session ID
 */
export const createChatSession = async (): Promise<{ session_id: string }> => {
	const supabase = getSupabaseBrowserClient();
	const {
		data: { session },
	} = await supabase.auth.getSession();
	try {
		const response = await fetch(`${API_BASE_URL}/sessions`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				...(session?.access_token && {
					Authorization: `Bearer ${session.access_token}`,
				}),
			},
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(errorData.detail || "Failed to create chat session");
		}

		const data = await response.json();
		if (!data.session_id) {
			console.warn(
				"Backend did not return a session_id. Generating a local one."
			);
			return { session_id: uuidv4() };
		}
		return { session_id: data.session_id };
	} catch (error) {
		console.error("Error creating chat session:", error);
		console.log("Generating a local session_id as a fallback.");
		return { session_id: uuidv4() };
	}
};
