import { CoachingInstruction } from '@/types/coaching'

const API_BASE_URL = '/api/coaching'

export async function fetchCoachingInstructions(): Promise<CoachingInstruction[]> {
  try {
    const response = await fetch(API_BASE_URL)
    if (!response.ok) {
      throw new Error('Failed to fetch coaching instructions')
    }
    return await response.json()
  } catch (error) {
    console.error('Error fetching coaching instructions:', error)
    throw error
  }
}

export async function createCoachingInstruction(
  instruction: Omit<CoachingInstruction, 'id' | 'created_at' | 'updated_at'>
): Promise<CoachingInstruction> {
  try {
    const response = await fetch(API_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(instruction),
    })

    if (!response.ok) {
      throw new Error('Failed to create coaching instruction')
    }

    return await response.json()
  } catch (error) {
    console.error('Error creating coaching instruction:', error)
    throw error
  }
}

export async function updateCoachingInstruction(
  id: string,
  updates: Partial<CoachingInstruction>
): Promise<CoachingInstruction> {
  try {
    const response = await fetch(`${API_BASE_URL}/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })

    if (!response.ok) {
      throw new Error('Failed to update coaching instruction')
    }

    return await response.json()
  } catch (error) {
    console.error('Error updating coaching instruction:', error)
    throw error
  }
}

export async function deleteCoachingInstruction(id: string): Promise<void> {
  try {
    const response = await fetch(`${API_BASE_URL}/${id}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      throw new Error('Failed to delete coaching instruction')
    }
  } catch (error) {
    console.error('Error deleting coaching instruction:', error)
    throw error
  }
}
