"use client";

import React, { useState, useEffect } from "react";
import { useChat } from "./hooks/useChat";
import { Sidebar } from "./components/Sidebar";
import { WelcomeScreen } from "./components/WelcomeScreen";
import { ChatArea } from "./components/ChatArea";
import { ChatInput } from "./components/ChatInput";
import { CoachingTab } from "./components/CoachingTab";
import { TracingTab } from "./components/TracingTab";

type ActiveTab = "chat" | "coaching" | "tracing";

export default function HotelAIAssistant() {
	const [activeTab, setActiveTab] = useState<ActiveTab>("chat");

	// Load active tab from localStorage on component mount
	useEffect(() => {
		const savedTab = localStorage.getItem("activeTab") as ActiveTab | null;
		if (savedTab && ["chat", "coaching", "tracing"].includes(savedTab)) {
			setActiveTab(savedTab);
		}
	}, []);

	// Save active tab to localStorage whenever it changes
	const handleTabChange = (tab: ActiveTab) => {
		setActiveTab(tab);
		localStorage.setItem("activeTab", tab);
	};
	const {
		chatHistory,
		currentChat,
		inputMessage,
		setInputMessage,
		setSelectedChat,
		isLoading,
		sessionId,
		currentStatus,
		messagesEndRef,
		handleNewChat,
		handleSendMessage,
		setCurrentChat,
		setIsLoading,
	} = useChat();

	const handlePromptClick = (prompt: string) => {
		setInputMessage(prompt);
		handleTabChange("chat");
	};

	// Render the main content based on the active tab
	const renderMainContent = () => {
		switch (activeTab) {
			case "coaching":
				return <CoachingTab onClose={() => setActiveTab("chat")} />;
			case "tracing":
				return <TracingTab onClose={() => setActiveTab("chat")} />;
			case "chat":
			default:
				return currentChat.length === 0 ? (
					<WelcomeScreen onPromptClick={handlePromptClick} />
				) : (
					<ChatArea
						currentChat={currentChat}
						isLoading={isLoading}
						currentStatus={currentStatus}
						messagesEndRef={messagesEndRef}
						sessionId={sessionId}
						setCurrentChat={setCurrentChat}
						setIsLoading={setIsLoading}
					/>
				);
		}
	};

	return (
		<div className="flex h-screen bg-gray-50">
			<Sidebar
				activeTab={activeTab}
				onTabChange={handleTabChange}
				onNewChat={handleNewChat}
				chatHistory={chatHistory}
				onSelectChat={(id: string) => {
					setSelectedChat(id);
					handleTabChange("chat");
				}}
			/>
			<div className="flex-1 flex flex-col">
				{renderMainContent()}
				{activeTab === "chat" && (
					<ChatInput
						inputMessage={inputMessage}
						onInputChange={setInputMessage}
						onSendMessage={handleSendMessage}
						isLoading={isLoading}
					/>
				)}
			</div>
		</div>
	);
}
