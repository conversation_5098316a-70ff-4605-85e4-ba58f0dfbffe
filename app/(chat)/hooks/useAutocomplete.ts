import { useState, useEffect, useCallback, useRef } from 'react';

export const useAutocomplete = (input: string, debounceTime = 500) => {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const suggestionsCache = useRef<Record<string, string[]>>({});

  const fetchSuggestions = useCallback(
    async (query: string) => {
      const trimmedQuery = query.trim();
      if (trimmedQuery.length < 3) {
        setSuggestions([]);
        return;
      }

      if (suggestionsCache.current[trimmedQuery]) {
        setSuggestions(suggestionsCache.current[trimmedQuery]);
        return;
      }

      console.log('Fetching suggestions for query:', trimmedQuery);
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/suggestions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ query: trimmedQuery }),
        });

        console.log('Suggestions API response status:', response.status);
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error('Suggestions API error response:', errorText);
          let errorMessage = `Error ${response.status}: Failed to fetch suggestions`;
          
          try {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.error || errorData.message || errorMessage;
          } catch (_e) {
            // If we can't parse the error as JSON, use the raw text
            if (errorText) {
              errorMessage = errorText;
            }
          }
          
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log('Suggestions API response data:', data);
        
        if (Array.isArray(data.suggestions)) {
          console.log('Setting suggestions:', data.suggestions);
          suggestionsCache.current[trimmedQuery] = data.suggestions;
          setSuggestions(data.suggestions);
        } else if (data.suggestions === undefined) {
          console.warn('No suggestions array in response, using empty array');
          setSuggestions([]);
        } else {
          console.warn('Unexpected suggestions format, using empty array. Data:', data);
          setSuggestions([]);
        }
      } catch (err) {
        console.error('Error in fetchSuggestions:', err);
        setError(err instanceof Error ? err.message : 'Failed to load suggestions');
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    const timerId = setTimeout(() => {
      fetchSuggestions(input);
    }, debounceTime);

    return () => {
      clearTimeout(timerId);
    };
  }, [input, debounceTime, fetchSuggestions]);

  const clearSuggestions = useCallback(() => {
    setSuggestions([]);
  }, []);

  return {
    suggestions,
    isLoading,
    error,
    clearSuggestions,
  };
};
