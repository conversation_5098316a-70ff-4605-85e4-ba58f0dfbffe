import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { createSupabaseBrowserClient } from '@/lib/utils/supabase-browser';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { ChatHistory } from '../types';
import {
  Plus,
  MessageSquare,
  Trash2,
  Sun,
  User,
  HelpCircle,
  LogOut,
  GraduationCap,
} from 'lucide-react';

interface SidebarProps {
  chatHistory: ChatHistory[];
  onNewChat: () => void;
  onSelectChat: (id: string) => void;
  activeTab: 'chat' | 'coaching' | 'tracing';
  onTabChange: (tab: 'chat' | 'coaching' | 'tracing') => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  chatHistory,
  onNewChat,
  onSelectChat,
  activeTab,
  onTabChange,
}) => {
  const router = useRouter();
  const supabase = createSupabaseBrowserClient();

  const handleLogout = async () => {
    await supabase.auth.signOut();
    router.push('/login');
  };

  return (
    <div className="w-64 bg-gray-900 text-white flex flex-col">
      <div className="p-4">
        <Button
          onClick={onNewChat}
          className={`w-full mb-2 ${activeTab === 'chat' ? 'bg-gray-800 text-white' : 'bg-gray-900 text-gray-300'}`}
        >
          <Plus className="w-4 h-4 mr-2" />
          New chat
        </Button>
        <Button 
          variant="outline" 
          className={`w-full mb-2 ${activeTab === 'chat' ? 'bg-gray-800 text-white' : 'bg-gray-900 text-gray-300'} border-gray-600`}
          onClick={() => onTabChange('chat')}
        >
          <MessageSquare className="w-4 h-4 mr-2" />
          Chat
        </Button>
        
        <Button 
          variant="outline" 
          className={`w-full mb-2 ${activeTab === 'coaching' ? 'bg-gray-800 text-white' : 'bg-gray-900 text-gray-300'} border-gray-600`}
          onClick={() => onTabChange('coaching')}
        >
          <GraduationCap className="w-4 h-4 mr-2" />
          AI Coaching
        </Button>
        <Button 
          variant="outline" 
          className={`w-full ${activeTab === 'tracing' ? 'bg-gray-800 text-white' : 'bg-gray-900 text-gray-300'} border-gray-600`}
          onClick={() => onTabChange('tracing')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 mr-2">
            <path d="M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8" />
            <path d="M21 3v5h-5" />
          </svg>
          Tracing
        </Button>
      </div>

      <ScrollArea className="flex-1 px-4">
        <div className="space-y-2">
          {chatHistory.map((chat) => (
            <Button
              key={chat.id}
              variant="ghost"
              className="w-full justify-start text-left text-gray-300 hover:bg-gray-800 hover:text-white"
              onClick={() => onSelectChat(chat.id)}
            >
              <MessageSquare className="w-4 h-4 mr-2 flex-shrink-0" />
              <span className="truncate">{chat.title}</span>
            </Button>
          ))}
        </div>
      </ScrollArea>

      <Separator className="bg-gray-700" />

      <div className="p-4 space-y-2">
        <Button variant="ghost" className="w-full justify-start text-gray-300 hover:bg-gray-800 hover:text-white">
          <Trash2 className="w-4 h-4 mr-2" />
          Clear conversations
        </Button>
        <Button variant="ghost" className="w-full justify-start text-gray-300 hover:bg-gray-800 hover:text-white">
          <Sun className="w-4 h-4 mr-2" />
          Light mode
        </Button>
        <Button variant="ghost" className="w-full justify-start text-gray-300 hover:bg-gray-800 hover:text-white">
          <User className="w-4 h-4 mr-2" />
          My account
        </Button>
        <Button variant="ghost" className="w-full justify-start text-gray-300 hover:bg-gray-800 hover:text-white">
          <HelpCircle className="w-4 h-4 mr-2" />
          Updates & FAQ
        </Button>
        <Button
          variant="ghost"
          className="w-full justify-start text-gray-300 hover:bg-gray-800 hover:text-white"
          onClick={handleLogout}
        >
          <LogOut className="w-4 h-4 mr-2" />
          Log out
        </Button>
      </div>
    </div>
  );
};
