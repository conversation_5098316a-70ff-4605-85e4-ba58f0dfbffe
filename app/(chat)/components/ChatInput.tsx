import React, { useRef, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Mic, Send, Loader2, Check } from 'lucide-react';
import { useAutocomplete } from '../hooks/useAutocomplete';

interface ChatInputProps {
  inputMessage: string;
  onInputChange: (value: string) => void;
  onSendMessage: (message: string) => void;
  isLoading: boolean;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  inputMessage,
  onInputChange,
  onSendMessage,
  isLoading,
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  
  const { suggestions, isLoading: isSuggestionsLoading, error: suggestionError } = useAutocomplete(inputMessage);

  // Debug log for suggestions and error state
  useEffect(() => {
    console.log('Current suggestions:', suggestions);
    if (suggestionError) {
      console.error('Suggestions error:', suggestionError);
    }
  }, [suggestions, suggestionError]);

  useEffect(() => {
    const hasValidSuggestions = suggestions.length > 0 && inputMessage.trim().length > 0;
    console.log('Updating showSuggestions:', { 
      hasValidSuggestions, 
      suggestionCount: suggestions.length,
      inputLength: inputMessage.trim().length,
      isSuggestionsLoading
    });
    
    if (hasValidSuggestions && !isSuggestionsLoading) {
      setShowSuggestions(true);
      setSelectedSuggestionIndex(-1);
    } else {
      setShowSuggestions(false);
    }
  }, [suggestions, inputMessage, isSuggestionsLoading]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const message = inputMessage.trim();
    if (message) {
      onSendMessage(message);
      onInputChange('');
      setShowSuggestions(false);
    }
  };

  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const message = inputMessage.trim();
    if (message) {
      onSendMessage(message);
      onInputChange('');
      setShowSuggestions(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    onInputChange(suggestion);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || suggestions.length === 0) {
      if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
        e.preventDefault();
        setShowSuggestions(true);
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        if (selectedSuggestionIndex >= 0) {
          e.preventDefault();
          onInputChange(suggestions[selectedSuggestionIndex]);
          setShowSuggestions(false);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        break;
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node) &&
          inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    console.log('Current suggestions:', suggestions);
    if (suggestionError) {
      console.error('Suggestions error:', suggestionError);
    }
  }, [suggestions, suggestionError]);

  useEffect(() => {
    const hasValidSuggestions = suggestions.length > 0 && inputMessage.trim().length > 0;
    console.log('Updating showSuggestions:', { 
      hasValidSuggestions, 
      suggestionCount: suggestions.length,
      inputLength: inputMessage.trim().length,
      isSuggestionsLoading
    });
    
    if (hasValidSuggestions && !isSuggestionsLoading) {
      setShowSuggestions(true);
      setSelectedSuggestionIndex(-1);
    } else {
      setShowSuggestions(false);
    }
  }, [suggestions, inputMessage, isSuggestionsLoading]);

  return (
    <div className="border-t bg-white p-4 shadow-md sticky bottom-0 z-10">
      <div className="max-w-3xl mx-auto">
        <div className="flex items-end space-x-2">
          <Button
            variant="ghost"
            size="icon"
            className="text-gray-400 hover:text-gray-600 flex-shrink-0 mb-1"
            disabled={isLoading}
          >
            <Mic className="w-4 h-4" />
          </Button>
          <div className="flex-1 flex flex-col relative" ref={suggestionsRef}>
            {/* Suggestions dropdown - positioned above the input */}
            {showSuggestions && suggestions.length > 0 && (
              <div className="absolute bottom-full left-0 right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg z-20 overflow-hidden">
                <div className="py-1 max-h-60 overflow-y-auto">
                  <div className="px-3 py-2 text-xs text-gray-500 font-medium border-b border-gray-100">
                    Suggestions
                  </div>
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      type="button"
                      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 flex items-center ${
                        selectedSuggestionIndex === index ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                      }`}
                      onClick={() => handleSuggestionClick(suggestion)}
                      onMouseEnter={() => setSelectedSuggestionIndex(index)}
                    >
                      <span className="flex-1 truncate">{suggestion}</span>
                      {selectedSuggestionIndex === index && (
                        <Check className="w-4 h-4 ml-2 text-blue-600 flex-shrink-0" />
                      )}
                    </button>
                  ))}
                </div>
                <div className="px-3 py-1.5 text-xs text-gray-500 border-t border-gray-100 flex items-center justify-between bg-gray-50">
                  <span>↑↓ to navigate</span>
                  <span>Enter to select</span>
                  <span>Esc to close</span>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit} className="flex-1 flex flex-col" ref={formRef}>
              <div className="relative flex-1 flex items-end space-x-2">
                <div className="relative flex-1">
                  <Input
                    ref={inputRef}
                    type="text"
                    placeholder="Type a message..."
                    value={inputMessage}
                    onChange={(e) => onInputChange(e.target.value)}
                    className="w-full min-w-0 pr-8"
                    disabled={isLoading}
                    onFocus={() => inputMessage.trim().length > 0 && setShowSuggestions(true)}
                    onKeyDown={(e) => {
                      handleKeyDown(e);
                      if (e.key === 'Enter' && !e.shiftKey && !showSuggestions) {
                        e.preventDefault();
                        handleSubmit(e);
                      }
                    }}
                  />
                  {isSuggestionsLoading && inputMessage.trim().length > 0 && (
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-4 h-4 animate-spin text-gray-400" />
                    </div>
                  )}
                </div>
                <Button
                  type="submit"
                  size="icon"
                  className="bg-blue-600 hover:bg-blue-700 text-white flex-shrink-0 h-10 w-10"
                  disabled={!inputMessage.trim() || isLoading}
                  onClick={handleButtonClick}
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>
        <p className="text-xs text-gray-400 mt-2 text-center">
          Press Enter to send, Shift+Enter for new line
        </p>
      </div>
    </div>
  );
};
