import React from 'react';
import {
  useCoachingInstructions,
  InstructionList,
  InstructionForm,
  Header,
} from './coaching';

interface CoachingTabProps {
  onClose: () => void;
}

export const CoachingTab: React.FC<CoachingTabProps> = ({ onClose }) => {
  const {
    instructions,
    isLoading,
    isAdding,
    setIsAdding,
    newInstruction,
    setNewInstruction,
    activeInstruction,
    toggleInstruction,
    updateInstruction,
    deleteInstruction,
    handleSaveInstruction,
    isSaving,
    editingInstruction,
    setEditingInstruction,
  } = useCoachingInstructions();

  return (
    <div className="flex flex-col h-full bg-gray-50 text-gray-800 p-0 overflow-hidden">
      <Header
        onClose={onClose}
        setIsAdding={setIsAdding}
        setEditingInstruction={setEditingInstruction}
        setNewInstruction={setNewInstruction}
        isAdding={isAdding}
      />

      <div className="flex-1 overflow-y-auto flex flex-col p-6">
        <InstructionList
          instructions={instructions}
          isLoading={isLoading}
          isAdding={isAdding}
          setIsAdding={setIsAdding}
          activeInstruction={activeInstruction}
          toggleInstruction={toggleInstruction}
          updateInstruction={updateInstruction}
          deleteInstruction={deleteInstruction}
        />
      </div>
      {isAdding && (
        <InstructionForm
          isSaving={isSaving}
          newInstruction={newInstruction}
          setNewInstruction={setNewInstruction}
          handleSaveInstruction={handleSaveInstruction}
          setIsAdding={setIsAdding}
          editingInstruction={editingInstruction}
        />
      )}
    </div>
  );
};
