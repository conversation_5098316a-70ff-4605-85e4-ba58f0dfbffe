import React from 'react';
import { Button } from '@/components/ui/button';
import { CoachingInstruction } from '@/types/coaching';

interface InstructionItemProps {
  instruction: CoachingInstruction;
  activeInstruction: string | null;
  toggleInstruction: (id: string) => void;
  updateInstruction: (id:string, updates: Partial<CoachingInstruction>) => void;
  deleteInstruction: (id: string) => void;
}

export const InstructionItem: React.FC<InstructionItemProps> = ({
  instruction,
  activeInstruction,
  toggleInstruction,
  updateInstruction,
  deleteInstruction,
}) => {
  return (
    <div className="grid grid-cols-12 gap-4 items-center p-4 border-b border-gray-200">
      <div className="col-span-4">
        <p className="font-medium">{instruction.title}</p>
        <p className="text-sm text-gray-500">{instruction.instruction}</p>
      </div>
      <div className="col-span-2 text-sm text-gray-500">Every message</div>
      <div className="col-span-2 text-sm text-gray-500">-</div>
      <div className="col-span-2 text-sm text-gray-500">
        {new Date(instruction.updated_at).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        })}
      </div>
      <div className="col-span-1">
        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${instruction.is_active ? 'bg-green-500' : 'bg-gray-300'}`}>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-white"><path d="M20 6 9 17l-5-5"></path></svg>
        </div>
      </div>
      <div className="col-span-1">
        <div className="relative">
          <Button variant="ghost" size="icon" onClick={() => toggleInstruction(instruction.id)}>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg>
          </Button>
          {activeInstruction === instruction.id && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
              <Button variant="ghost" className="w-full text-left" onClick={() => {
                // @ts-expect-error - The updateInstruction function is being called with a third parameter (true) that isn't in the type definition, but it's needed for the edit functionality to work correctly.
                updateInstruction(instruction.id, instruction, true);
              }}>
                Edit
              </Button>
              <Button variant="ghost" className="w-full text-left text-red-600" onClick={() => deleteInstruction(instruction.id)}>
                Delete
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
