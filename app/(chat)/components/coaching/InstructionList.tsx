import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2 } from 'lucide-react';
import { CoachingInstruction } from '@/types/coaching';
import { InstructionItem } from './InstructionItem';

interface InstructionListProps {
  instructions: CoachingInstruction[];
  isLoading: boolean;
  isAdding: boolean;
  setIsAdding: (isAdding: boolean) => void;
  activeInstruction: string | null;
  toggleInstruction: (id: string) => void;
  updateInstruction: (id: string, updates: Partial<CoachingInstruction>) => void;
  deleteInstruction: (id: string) => void;
}

const Loader = () => (
  <div className="flex flex-col items-center justify-center h-64 rounded-xl bg-white p-8 shadow-sm border border-gray-100">
    <Loader2 className="h-10 w-10 animate-spin text-blue-500 mb-3" />
    <p className="text-gray-600">Loading your coaching instructions...</p>
  </div>
);

export const InstructionList: React.FC<InstructionListProps> = ({
  instructions,
  isLoading,
  activeInstruction,
  toggleInstruction,
  updateInstruction,
  deleteInstruction,
}) => {
  return (
    <ScrollArea className="flex-1 -mr-4 pr-4">
      <div className="space-y-4">
        {isLoading ? (
          <Loader />
        ) : (
          <>
            <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-500 px-4">
              <div className="col-span-4">Details</div>
              <div className="col-span-2">Availability</div>
              <div className="col-span-2">Conversations</div>
              <div className="col-span-2">Last modified</div>
              <div className="col-span-1">Active</div>
              <div className="col-span-1"></div>
            </div>
            {instructions.map((instruction) => (
              <InstructionItem
                key={instruction.id}
                instruction={instruction}
                activeInstruction={activeInstruction}
                toggleInstruction={toggleInstruction}
                updateInstruction={updateInstruction}
                deleteInstruction={deleteInstruction}
              />
            ))}
          </>
        )}
      </div>
    </ScrollArea>
  );
};
