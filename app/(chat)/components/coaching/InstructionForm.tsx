import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Textarea from './Textarea';
import { X } from 'lucide-react';
import { NewCoachingInstruction, CoachingInstruction } from '@/types/coaching';

interface InstructionFormProps {
  isSaving: boolean;
  newInstruction: NewCoachingInstruction;
  setNewInstruction: (instruction: NewCoachingInstruction) => void;
  handleSaveInstruction: () => void;
  setIsAdding: (isAdding: boolean) => void;
  editingInstruction: CoachingInstruction | null;
}

export const InstructionForm: React.FC<InstructionFormProps> = ({
  isSaving,
  newInstruction,
  setNewInstruction,
  handleSaveInstruction,
  setIsAdding,
  editingInstruction,
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-8 w-full max-w-2xl">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {editingInstruction ? 'Edit Instruction' : 'New Instruction'}
          </h2>
          <Button variant="ghost" size="icon" onClick={() => setIsAdding(false)}>
            <X className="h-6 w-6" />
          </Button>
        </div>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Title</label>
            <Input
              placeholder="e.g., Password Reset"
              value={newInstruction.title}
              onChange={(e) => setNewInstruction({ ...newInstruction, title: e.target.value })}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Instruction</label>
            <Textarea
              placeholder="The AI should respond with..."
              value={newInstruction.instruction}
              onChange={(e) => setNewInstruction({ ...newInstruction, instruction: e.target.value })}
              className="min-h-[120px]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Scenario</label>
            <Input
              placeholder="When does this instruction apply?"
              value={newInstruction.scenario || ''}
              onChange={(e) => setNewInstruction({ ...newInstruction, scenario: e.target.value })}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Category</label>
            <Input
              placeholder="e.g., Technical, Behavioral, Process"
              value={newInstruction.category || ''}
              onChange={(e) => setNewInstruction({ ...newInstruction, category: e.target.value })}
            />
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Priority (1-5)</label>
              <select
                value={newInstruction.priority}
                onChange={(e) => setNewInstruction({ ...newInstruction, priority: parseInt(e.target.value) })}
                className="bg-white border border-gray-300 text-gray-900 rounded px-3 py-2 w-full"
              >
                <option value={1}>1 - Highest</option>
                <option value={2}>2 - High</option>
                <option value={3}>3 - Medium</option>
                <option value={4}>4 - Low</option>
                <option value={5}>5 - Lowest</option>
              </select>
            </div>
            <div className="flex items-center space-x-2 pt-6">
              <input
                type="checkbox"
                id="is_active"
                checked={newInstruction.is_active}
                onChange={(e) => setNewInstruction({ ...newInstruction, is_active: e.target.checked })}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="is_active" className="text-sm">Active</label>
            </div>
          </div>
          <div className="flex justify-end space-x-4">
            <Button variant="outline" onClick={() => setIsAdding(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveInstruction} disabled={isSaving}>
              {isSaving ? 'Saving...' : 'Save Instruction'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
