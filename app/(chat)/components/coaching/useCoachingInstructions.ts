import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  createCoachingInstruction, 
  deleteCoachingInstruction, 
  fetchCoachingInstructions, 
  updateCoachingInstruction 
} from '../../../coaching/coachingService';
import { CoachingInstruction } from '@/types/coaching';

export const useCoachingInstructions = () => {
  const [instructions, setInstructions] = useState<CoachingInstruction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAdding, setIsAdding] = useState(false);
  const [newInstruction, setNewInstruction] = useState<Omit<CoachingInstruction, 'id' | 'created_at' | 'updated_at'>>({ 
    title: '',
    instruction: '',
    priority: 3,
    is_active: true,
    category: '',
    scenario: ''
  });
  const [activeInstruction, setActiveInstruction] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [editingInstruction, setEditingInstruction] = useState<CoachingInstruction | null>(null);

  useEffect(() => {
    const loadInstructions = async () => {
      try {
        const data = await fetchCoachingInstructions();
        setInstructions(data);
      } catch (error) {
        console.error('Failed to load instructions:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadInstructions();
  }, []);

  const toggleInstruction = (id: string) => {
    setActiveInstruction(activeInstruction === id ? null : id);
  };

  const addInstruction = async () => {
    if (!newInstruction.title || !newInstruction.instruction) return;
    
    setIsSaving(true);
    const toastId = toast.loading('Creating instruction...');
    try {
      const createdInstruction = await createCoachingInstruction(newInstruction);
      setInstructions([...instructions, createdInstruction].sort((a, b) => a.priority - b.priority));
      setNewInstruction({ 
        title: '',
        instruction: '',
        priority: 3,
        is_active: true,
        category: '',
        scenario: ''
      });
      setIsAdding(false);
      toast.success('Instruction created successfully!', { id: toastId });
    } catch (error) {
      console.error('Failed to create instruction:', error);
      toast.error('Failed to create instruction.', { id: toastId });
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdateInstruction = async (id: string, updates: Partial<CoachingInstruction>, isEditing?: boolean) => {
    if (isEditing) {
      setEditingInstruction(updates as CoachingInstruction);
      setNewInstruction(updates as CoachingInstruction);
      setIsAdding(true);
    } else {
      setIsSaving(true);
      const toastId = toast.loading('Updating instruction...');
      try {
        const updatedInstruction = await updateCoachingInstruction(id, updates);
        setInstructions(instructions.map(inst => 
          inst.id === id ? updatedInstruction : inst
        ));
        setIsAdding(false);
        setEditingInstruction(null);
        toast.success('Instruction updated successfully!', { id: toastId });
      } catch (error) {
        console.error('Failed to update instruction:', error);
        toast.error('Failed to update instruction.', { id: toastId });
      } finally {
        setIsSaving(false);
      }
    }
  };

  const handleDeleteInstruction = async (id: string) => {
    const toastId = toast.loading('Deleting instruction...');
    try {
      await deleteCoachingInstruction(id);
      setInstructions(instructions.filter(inst => inst.id !== id));
      toast.success('Instruction deleted successfully!', { id: toastId });
    } catch (error) {
      console.error('Failed to delete instruction:', error);
      toast.error('Failed to delete instruction.', { id: toastId });
    }
  };

  const handleSaveInstruction = () => {
    if (editingInstruction) {
      handleUpdateInstruction(editingInstruction.id, newInstruction);
    } else {
      addInstruction();
    }
  };

  return {
    instructions,
    isLoading,
    isAdding,
    setIsAdding,
    newInstruction,
    setNewInstruction,
    activeInstruction,
    toggleInstruction,
    updateInstruction: handleUpdateInstruction,
    deleteInstruction: handleDeleteInstruction,
    handleSaveInstruction,
    isSaving,
    editingInstruction,
    setEditingInstruction,
  };
};
