import React from 'react';
import { cn } from '@/lib/utils';

// Enhanced Textarea with better styling
const Textarea = React.forwardRef<HTMLTextAreaElement, React.TextareaHTMLAttributes<HTMLTextAreaElement>>(
  ({ className = '', ...props }, ref) => (
    <div className="relative">
      <textarea
        ref={ref}
        className={cn(
          'flex min-h-[120px] w-full rounded-md border border-gray-300 bg-white text-gray-900',
          'px-3 py-2 text-sm ring-offset-background',
          'placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2',
          'focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          'transition-colors duration-200 ease-in-out',
          className
        )}
        {...props}
      />
    </div>
  )
);
Textarea.displayName = 'Textarea';

export default Textarea;
