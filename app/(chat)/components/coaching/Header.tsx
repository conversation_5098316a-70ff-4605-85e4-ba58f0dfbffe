import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { NewCoachingInstruction } from '@/types/coaching';

interface HeaderProps {
  onClose: () => void;
  setIsAdding: (isAdding: boolean) => void;
  setEditingInstruction: (instruction: null) => void;
  setNewInstruction: (instruction: NewCoachingInstruction) => void;
  isAdding: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  setIsAdding,
  isAdding,
}) => {
  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Coaching</h1>
          <p className="text-sm text-gray-500 mt-1">
            Continuously improve your AI Agent performance by coaching it on how to make decisions, handle tone or certain topics, or retrieve information.
          </p>
        </div>
        <Button 
          onClick={() => setIsAdding(true)}
          disabled={isAdding}
          className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-sm transition-all duration-200 transform hover:scale-105"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Instruction
        </Button>
      </div>
      <div className="mt-4 flex items-center space-x-4">
        <Button variant="outline" className="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg>
          <span>Last 7 days</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="m6 9 6 6 6-6"></path></svg>
        </Button>
        <div className="relative flex-grow">
          <input type="text" placeholder="Search" className="w-full pl-10 pr-4 py-2 border rounded-md" />
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"><circle cx="11" cy="11" r="8"></circle><line x1="21" x2="16.65" y1="21" y2="16.65"></line></svg>
        </div>
        <div className="flex items-center space-x-2">
          <label htmlFor="test-conversations" className="text-sm font-medium">Include test conversations</label>
          <button role="switch" aria-checked="false" className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-300">
            <span className="inline-block h-4 w-4 transform rounded-full bg-white transition translate-x-1"></span>
          </button>
        </div>
      </div>
    </div>
  );
};
