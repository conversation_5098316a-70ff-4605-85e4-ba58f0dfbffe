import React from 'react';

// Priority badge component with improved styling and icons
export const PriorityBadge = ({ priority }: { priority: number }) => {
  const priorityConfig = [
    { 
      label: 'P1', 
      color: 'bg-red-100 text-red-800 border-red-200',
      icon: '❗',
      title: 'Highest Priority'
    },
    { 
      label: 'P2', 
      color: 'bg-orange-100 text-orange-800 border-orange-200',
      icon: '🔺',
      title: 'High Priority'
    },
    { 
      label: 'P3', 
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      icon: '🔸',
      title: 'Medium Priority'
    },
    { 
      label: 'P4', 
      color: 'bg-blue-100 text-blue-800 border-blue-200',
      icon: '🔹',
      title: 'Low Priority'
    },
    { 
      label: 'P5', 
      color: 'bg-green-100 text-green-800 border-green-200',
      icon: '▫️',
      title: 'Lowest Priority'
    },
  ];
  
  const config = priorityConfig[priority - 1] || priorityConfig[2];
  
  return (
    <span 
      className={`inline-flex items-center text-xs font-medium px-2.5 py-0.5 rounded-full border ${config.color} shadow-sm`}
      title={config.title}
    >
      <span className="mr-1">{config.icon}</span>
      {config.label}
    </span>
  );
};

// Category badge component with improved styling
export const CategoryBadge = ({ category }: { category: string }) => (
  <span className="inline-flex items-center text-xs font-medium px-2.5 py-0.5 rounded-full bg-indigo-100 text-indigo-800 border border-indigo-200 shadow-sm">
    <span className="w-1.5 h-1.5 rounded-full bg-indigo-500 mr-1.5"></span>
    {category || 'General'}
  </span>
);
