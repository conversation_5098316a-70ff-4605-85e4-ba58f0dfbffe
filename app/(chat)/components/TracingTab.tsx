"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, RefreshCw, X } from "lucide-react";
import { SessionList } from "@/app/tracing/components/SessionList";
import { TraceDetails } from "@/app/tracing/components/TraceDetails";
import { useTraces } from "@/app/tracing/hooks/useTraces";
import { SessionGroup } from "@/app/tracing/types";

interface TracingTabProps {
	onClose?: () => void;
	sessionId?: string;
}

export const TracingTab: React.FC<TracingTabProps> = ({ onClose }) => {
	const handleClose = () => {
		if (onClose) {
			onClose();
		}
	};
	const [sessionIdFilter, setSessionIdFilter] = useState("");
	const [debouncedFilter, setDebouncedFilter] = useState("");
	const [selectedSession, setSelectedSession] = useState<SessionGroup | null>(
		null
	);

	const {
		sessionGroups = [],
		loading,
		loadingMore,
		error,
		nextToken,
		handleRefresh,
		handleLoadMore,
	} = useTraces(debouncedFilter) || {};

	// Debug logging
	useEffect(() => {
		console.log("TracingTab - sessionGroups:", sessionGroups);
		console.log("TracingTab - sessionGroups length:", sessionGroups?.length);
		console.log("TracingTab - loading:", loading);
		console.log("TracingTab - error:", error);
		console.log("TracingTab - debouncedFilter:", debouncedFilter);
	}, [sessionGroups, loading, error, debouncedFilter]);

	useEffect(() => {
		if (sessionGroups.length > 0 && !selectedSession) {
			setSelectedSession(sessionGroups[0]);
		}
	}, [sessionGroups, selectedSession]);

	useEffect(() => {
		const handler = setTimeout(() => {
			setDebouncedFilter(sessionIdFilter);
		}, 500);

		return () => {
			clearTimeout(handler);
		};
	}, [sessionIdFilter]);

	const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSessionIdFilter(e.target.value);
	};

	const handleSessionSelect = (session: SessionGroup) => {
		setSelectedSession(session);
	};

	const handleFilterSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		setDebouncedFilter(sessionIdFilter);
	};

	return (
		<div className="flex flex-col h-full">
			<div className="flex justify-between items-center p-4 border-b">
				<h2 className="text-lg font-semibold">Tracing</h2>
				<div className="flex items-center space-x-2">
					<div className="flex space-x-2">
						<Button
							variant="ghost"
							size="sm"
							onClick={handleRefresh}
							disabled={loading || loadingMore}
						>
							<RefreshCw
								className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`}
							/>
							Refresh
						</Button>
						<Button
							variant="ghost"
							size="sm"
							onClick={handleClose}
							className="text-gray-400 hover:text-gray-100"
						>
							<X className="w-4 h-4" />
						</Button>
					</div>
				</div>
			</div>

			<div className="flex flex-1 overflow-hidden">
				<div className="w-1/3 border-r overflow-y-auto">
					<div className="p-4">
						<form onSubmit={handleFilterSubmit} className="mb-4">
							<div className="relative">
								<input
									type="text"
									placeholder="Filter by session ID..."
									value={sessionIdFilter}
									onChange={handleFilterChange}
									className="w-full px-3 py-2 border rounded-md text-sm"
								/>
							</div>
						</form>

						{loading ? (
							<div className="flex justify-center p-4">
								<Loader2 className="w-6 h-6 animate-spin text-gray-400" />
							</div>
						) : error ? (
							<div className="text-red-500 text-sm p-4">{error}</div>
						) : (
							<>
								{/* Debug info */}
								<div className="text-xs text-gray-500 p-2 bg-gray-50 mb-2">
									Debug: {sessionGroups?.length || 0} sessions, loading:{" "}
									{loading ? "yes" : "no"}, error: {error || "none"}
								</div>
								<SessionList
									sessionGroups={sessionGroups}
									selectedSession={selectedSession}
									onSelectSession={handleSessionSelect}
									loading={loading}
									error={error}
									nextToken={nextToken}
									loadingMore={loadingMore}
									onLoadMore={handleLoadMore}
								/>
							</>
						)}
					</div>
				</div>

				<div className="flex-1 overflow-y-auto p-4">
					{selectedSession ? (
						<TraceDetails selectedSession={selectedSession} />
					) : (
						<div className="flex items-center justify-center h-full text-gray-500">
							Select a session to view details
						</div>
					)}
				</div>
			</div>
		</div>
	);
};
