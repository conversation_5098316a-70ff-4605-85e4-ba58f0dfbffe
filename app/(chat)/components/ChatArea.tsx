import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Message } from "../types";
import AgentResponseHandler from "@/components/AgentResponseHandler";
import { sendChatMessage } from "@/lib/api";
import { toast } from "sonner";

interface ChatAreaProps {
	currentChat: Message[];
	isLoading: boolean;
	currentStatus?: string | null;
	messagesEndRef: React.RefObject<HTMLDivElement | null>;
	sessionId: string | null;
	setCurrentChat: React.Dispatch<React.SetStateAction<Message[]>>;
	setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

export const ChatArea: React.FC<ChatAreaProps> = ({
	currentChat,
	isLoading,
	currentStatus,
	messagesEndRef,
	sessionId,
	setCurrentChat,
	setIsLoading,
}) => {
	return (
		<div className="flex-1 flex flex-col h-[calc(100vh-200px)]">
			<ScrollArea className="flex-1 p-4 overflow-y-auto">
				<div className="max-w-3xl mx-auto space-y-4 pb-4">
					{currentChat.map((message) => (
						<div
							key={message.id}
							className={`flex ${
								message.isUser ? "justify-end" : "justify-start"
							} w-full`}
						>
							<div
								className={`max-w-[80%] lg:max-w-[70%] px-4 py-3 rounded-xl ${
									message.isUser
										? "bg-blue-600 text-white rounded-br-none"
										: "bg-white border border-gray-200 text-gray-900 rounded-bl-none"
								} shadow-sm`}
							>
								{message.isUser ? (
									<p className="whitespace-pre-wrap break-words text-sm md:text-base">
										{typeof message.content === "string"
											? message.content
											: JSON.stringify(message.content)}
									</p>
								) : (
									<AgentResponseHandler
										message={message.content}
										onFormSubmit={async (formData: Record<string, unknown>) => {
											try {
												if (!sessionId) {
													throw new Error("Session ID is not available");
												}

												const userMessage: Message = {
													id: Date.now().toString(),
													content: `Form submitted: ${JSON.stringify(
														formData,
														null,
														2
													)}`,
													isUser: true,
													timestamp: new Date(),
												};

												setCurrentChat((prev) => [...prev, userMessage]);
												setIsLoading(true);

												const response = await sendChatMessage(
													`Form submitted: ${JSON.stringify(formData)}`,
													sessionId
												);

												const responseType = (response.response_type ||
													"text") as "text" | "form";
												const responseContent = response.response;

												const aiResponse: Message = {
													id: (Date.now() + 1).toString(),
													content: responseContent,
													isUser: false,
													timestamp: new Date(),
													metadata: {
														responseType,
														...(responseType === "form" && {
															formData:
																typeof response.response === "object"
																	? response.response
																	: {},
														}),
													},
												};

												setCurrentChat((prev) => [...prev, aiResponse]);
												return response;
											} catch (error) {
												console.error("Error submitting form:", error);
												toast.error("Failed to submit form");
												throw error;
											} finally {
												setIsLoading(false);
											}
										}}
									/>
								)}
								<p
									className={`text-xs mt-1 text-right ${
										message.isUser ? "text-blue-100" : "text-gray-500"
									}`}
								>
									{new Date(message.timestamp).toLocaleTimeString([], {
										hour: "2-digit",
										minute: "2-digit",
									})}
								</p>
							</div>
						</div>
					))}
					{isLoading && (
						<div className="flex justify-start w-full">
							<div className="max-w-[80%] lg:max-w-[70%] px-4 py-3 rounded-lg bg-white border border-gray-200">
								<div className="flex items-center space-x-3">
									<div className="flex space-x-1">
										<div
											className="w-2 h-2 rounded-full bg-blue-500 animate-bounce"
											style={{ animationDelay: "0ms" }}
										></div>
										<div
											className="w-2 h-2 rounded-full bg-blue-500 animate-bounce"
											style={{ animationDelay: "150ms" }}
										></div>
										<div
											className="w-2 h-2 rounded-full bg-blue-500 animate-bounce"
											style={{ animationDelay: "300ms" }}
										></div>
									</div>
									{currentStatus && (
										<span className="text-sm text-gray-600 animate-pulse">
											{currentStatus}
										</span>
									)}
								</div>
							</div>
						</div>
					)}
					<div ref={messagesEndRef} className="h-4" />
				</div>
			</ScrollArea>
		</div>
	);
};
