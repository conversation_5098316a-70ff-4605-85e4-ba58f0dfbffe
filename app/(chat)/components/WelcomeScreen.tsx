import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  Hotel,
  DollarSign,
  BarChart3,
} from 'lucide-react';

const samplePrompts = {
  pricing: [
    "Hike double room price by 15% for Easter week",
    "Update all rooms price for Christmas week",
    "Set weekend premium pricing for December",
    "Adjust rates for peak summer season",
    "Create special pricing for corporate clients",
  ],
  analytics: [
    "my sales for single room during last Christmas?",
    "Show me last month's occupancy rates",
    "Compare this year's revenue to last year",
    "Which room types have highest demand?",
    "Analyze booking patterns by guest type",
  ],
  operations: [
    "Optimize pricing for New Year's Eve",
    "Suggest pricing strategy for low season",
    "Analyze competitor pricing in my area",
    "Forecast demand for next quarter",
    "Identify best performing amenities",
  ],
};

interface WelcomeScreenProps {
  onPromptClick: (prompt: string) => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onPromptClick }) => {
  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8">
      <div className="text-center mb-12">
        <div className="flex items-center justify-center mb-4">
          <Hotel className="w-8 h-8 text-blue-600 mr-3" />
          <h1 className="text-3xl font-bold text-gray-900">HotelAI</h1>
          <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800">
            Pro
          </Badge>
        </div>
        <p className="text-gray-600">Your intelligent hotel management assistant</p>
      </div>

      <div className="mb-8 max-w-6xl w-full">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.entries(samplePrompts).map(([category, prompts]) => (
            <Card key={category} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {category === 'pricing' && <DollarSign className="w-5 h-5 text-blue-600 mr-2 flex-shrink-0" />}
                  {category === 'analytics' && <BarChart3 className="w-5 h-5 text-green-600 mr-2 flex-shrink-0" />}
                  {category === 'operations' && <Hotel className="w-5 h-5 text-purple-600 mr-2 flex-shrink-0" />}
                  <h4 className="font-medium text-gray-900 capitalize">{category}</h4>
                </div>
                <div className="space-y-3">
                  {prompts.map((prompt, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      className="w-full justify-start text-left text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 h-auto p-3 text-wrap"
                      onClick={() => onPromptClick(prompt)}
                    >
                      <span className="text-left break-words">{prompt}</span>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};
