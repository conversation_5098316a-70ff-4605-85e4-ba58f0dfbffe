import { useState, useCallback, useEffect } from "react";
import { Trace, SessionGroup, SupabaseTrace } from "../types";

// Group traces by session ID
const groupTracesBySession = (traces: Trace[]): SessionGroup[] => {
	const sessionMap = new Map<string, SessionGroup>();

	// Process each trace and group by session
	traces.forEach((trace) => {
		// Skip invalid traces
		if (!trace || !trace.sessionId) return;

		// Initialize session if it doesn't exist
		if (!sessionMap.has(trace.sessionId)) {
			sessionMap.set(trace.sessionId, {
				sessionId: trace.sessionId,
				traces: [],
				agentTypes: [],
				status: "completed",
				latestTimestamp: trace.createdAt || trace.timestamp,
				errorCount: 0,
			});
		}

		const session = sessionMap.get(trace.sessionId)!;

		// Add trace to session
		session.traces.push(trace);

		// Update agent types
		const agentType = trace.agent_type || trace.traceData?.agent_type;
		if (agentType && !session.agentTypes.includes(agentType)) {
			session.agentTypes.push(agentType);
		}

		// Update latest timestamp
		const traceTimestamp = trace.createdAt || trace.timestamp;
		if (new Date(traceTimestamp) > new Date(session.latestTimestamp)) {
			session.latestTimestamp = traceTimestamp;
		}

		// Update error count
		if (trace.error) {
			session.errorCount++;
		}

		// Update session status based on trace status
		const traceStatus = trace.status || trace.traceData?.status || "completed";
		if (traceStatus === "error") {
			session.status = "error";
		} else if (traceStatus === "processing" && session.status !== "error") {
			session.status = "processing";
		} else if (traceStatus === "started" && session.status === "completed") {
			session.status = "started";
		}
	});

	sessionMap.forEach((session) => {
		session.traces.sort(
			(a, b) =>
				new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
		);
	});

	return Array.from(sessionMap.values()).sort(
		(a, b) =>
			new Date(b.latestTimestamp).getTime() -
			new Date(a.latestTimestamp).getTime()
	);
};

export const useTraces = (sessionIdFilter: string) => {
	const [traces, setTraces] = useState<Trace[]>([]);
	const [sessionGroups, setSessionGroups] = useState<SessionGroup[]>([]);
	const [loading, setLoading] = useState(false);
	const [loadingMore, setLoadingMore] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [nextToken, setNextToken] = useState<string | null>(null);
	const [lastRefreshed, setLastRefreshed] = useState<Date | null>(null);

	// Stable function using functional state updates
	const fetchTracesStable = useCallback(
		async (loadMore = false, currentSessionFilter = sessionIdFilter) => {
			// Use functional state update to check current loading state
			const shouldProceed = await new Promise<boolean>((resolve) => {
				setLoading((currentLoading) => {
					setLoadingMore((currentLoadingMore) => {
						const proceed =
							!currentLoading && !(loadMore && currentLoadingMore);
						resolve(proceed);
						return currentLoadingMore;
					});
					return currentLoading;
				});
			});

			if (!shouldProceed) return;

			try {
				if (loadMore) {
					setLoadingMore(true);
				} else {
					setLoading(true);
					setError(null);
				}

				// Get current nextToken
				const currentNextToken = await new Promise<string | null>((resolve) => {
					setNextToken((token) => {
						resolve(token);
						return token;
					});
				});

				// Use Next.js API route for secure Supabase access
				const params = new URLSearchParams();
				if (currentSessionFilter.trim()) {
					params.append("session_id", currentSessionFilter.trim());
				}
				params.append("limit", "100");
				if (loadMore && currentNextToken) {
					params.append("offset", currentNextToken);
				}

				const response = await fetch(`/api/tracing?${params.toString()}`);
				if (!response.ok) {
					const errorText = await response.text();
					throw new Error(`API Error: ${response.status} - ${errorText}`);
				}

				const data = await response.json();

				if (!data.traces || !Array.isArray(data.traces)) {
					if (!loadMore) {
						setTraces([]);
						setSessionGroups([]);
					}
					return;
				}

				// Process the traces to ensure they have the expected structure
				const processedTraces: Trace[] = data.traces.map(
					(trace: SupabaseTrace) => ({
						id: trace.id,
						sessionId: trace.session_id,
						timestamp: trace.created_at,
						createdAt: trace.created_at,
						agent_type: trace.agent_type,
						status: trace.status,
						error: trace.trace_data?.error,
						metadata: trace.metadata || {},
						duration_seconds: trace.duration_seconds || undefined,
						userMessage: trace.user_message || undefined,
						toolCalls: trace.tool_calls || [],
						aiResponse: trace.ai_response || undefined,
						user_id: trace.user_id || undefined,
						start_time: trace.start_time || undefined,
						end_time: trace.end_time || undefined,
						updated_at: trace.updated_at,
						traceData: trace.trace_data || undefined,
					})
				);

				setTraces((prevTraces) => {
					const newTraces = loadMore
						? [...prevTraces, ...processedTraces]
						: processedTraces;
					const groups = groupTracesBySession(newTraces);
					setSessionGroups(groups);
					return newTraces;
				});

				setNextToken(data.next_token);
				setLastRefreshed(new Date());
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : "An unknown error occurred";
				setError(errorMessage);
			} finally {
				setLoading(false);
				setLoadingMore(false);
			}
		},
		[sessionIdFilter] // Only sessionIdFilter as dependency
	);

	const handleRefresh = useCallback(() => {
		setError(null); // Clear any previous errors
		setNextToken(null);
		fetchTracesStable(false, sessionIdFilter);
	}, [fetchTracesStable, sessionIdFilter]);

	const handleLoadMore = useCallback(() => {
		// Use functional update to check current state
		setNextToken((currentNextToken) => {
			if (currentNextToken) {
				setLoadingMore((isLoadingMore) => {
					if (!isLoadingMore) {
						fetchTracesStable(true, sessionIdFilter);
					}
					return isLoadingMore;
				});
			}
			return currentNextToken;
		});
	}, [fetchTracesStable, sessionIdFilter]);

	useEffect(() => {
		// Only fetch on initial load and when sessionIdFilter changes
		fetchTracesStable(false, sessionIdFilter);
	}, [sessionIdFilter, fetchTracesStable]);

	// Auto-refresh every 2 minutes (120 seconds) - only when not in error state
	useEffect(() => {
		if (error) return; // Don't auto-refresh if there's an error

		const interval = setInterval(() => {
			// Use functional updates to check current state
			setLoading((isLoading) => {
				if (!isLoading) {
					setLoadingMore((isLoadingMore) => {
						if (!isLoadingMore && !error) {
							console.log("Auto-refreshing traces...");
							fetchTracesStable(false, sessionIdFilter);
						}
						return isLoadingMore;
					});
				}
				return isLoading;
			});
		}, 120000); // 2 minutes

		return () => clearInterval(interval);
	}, [error, fetchTracesStable, sessionIdFilter]);

	return {
		traces,
		sessionGroups,
		loading,
		loadingMore,
		error,
		nextToken,
		lastRefreshed,
		handleRefresh,
		handleLoadMore,
		fetchTraces: fetchTracesStable,
	};
};
