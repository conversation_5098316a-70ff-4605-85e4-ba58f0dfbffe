import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RefreshCw } from "lucide-react";
import { format } from "date-fns";

interface HeaderProps {
	loading: boolean;
	tracesCount: number;
	lastRefreshed: Date | null;
	sessionIdFilter: string;
	onFilterChange: (value: string) => void;
	onFilterSubmit: (e: React.FormEvent) => void;
	onRefresh: () => void;
}

export const Header: React.FC<HeaderProps> = ({
	loading,
	tracesCount,
	lastRefreshed,
	sessionIdFilter,
	onFilterChange,
	onFilterSubmit,
	onRefresh,
}) => {
	return (
		<div className="flex-shrink-0 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 bg-white border-b">
			<div>
				<h1 className="text-xl font-bold">Tracing Dashboard</h1>
				<p className="text-xs text-gray-500">
					{loading && tracesCount === 0 ? (
						"Loading..."
					) : (
						<>
							Auto-refreshing every 2 minutes • {tracesCount} trace
							{tracesCount !== 1 ? "s" : ""} found
							{lastRefreshed && (
								<> • Last updated: {format(lastRefreshed, "HH:mm:ss")}</>
							)}
						</>
					)}
				</p>
			</div>
			<div className="flex items-center space-x-2">
				<form onSubmit={onFilterSubmit} className="flex space-x-2">
					<Input
						placeholder="Filter by Session ID"
						value={sessionIdFilter}
						onChange={(e) => onFilterChange(e.target.value)}
						className="w-64"
					/>
					<Button type="submit" variant="outline" disabled={loading}>
						Filter
					</Button>
				</form>
				<Button
					onClick={onRefresh}
					variant="outline"
					disabled={loading}
					className="flex items-center space-x-2"
				>
					<RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
					Refresh
				</Button>
			</div>
		</div>
	);
};
