import React, { useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Loader2, ChevronR<PERSON>, Clock, AlertCircle, CheckCircle, XCircle, Zap, MessageSquare, Wrench } from 'lucide-react';
import { SessionGroup, Trace } from '../types';
import { formatDistanceToNow } from '../utils';

interface SessionListProps {
  sessionGroups: SessionGroup[];
  selectedSession: SessionGroup | null;
  onSelectSession: (session: SessionGroup) => void;
  loading: boolean;
  error: string | null;
  nextToken: string | null;
  loadingMore: boolean;
  onLoadMore: () => void;
}

export const SessionList: React.FC<SessionListProps> = ({
  sessionGroups,
  selectedSession,
  onSelectSession,
  loading,
  error,
  nextToken,
  loadingMore,
  onLoadMore,
}) => {
  // Calculate session statistics
  const sessionStats = useMemo(() => {
    return {
      totalSessions: sessionGroups.length,
      activeSessions: sessionGroups.filter(s => s.status === 'in_progress' || s.status === 'processing').length,
      completedSessions: sessionGroups.filter(s => s.status === 'completed').length,
      errorSessions: sessionGroups.filter(s => s.errorCount > 0).length,
    };
  }, [sessionGroups]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-3.5 w-3.5 text-green-500" />;
      case 'error':
      case 'failed':
        return <XCircle className="h-3.5 w-3.5 text-red-500" />;
      case 'processing':
      case 'in_progress':
        return <Loader2 className="h-3.5 w-3.5 animate-spin text-blue-500" />;
      default:
        return <Zap className="h-3.5 w-3.5 text-yellow-500" />;
    }
  };

  const getToolCallCount = (traces: Trace[]) => {
    return traces.reduce((count, trace) => {
      const toolCalls = trace.traceData?.tool_calls || trace.toolCalls || [];
      return count + (Array.isArray(toolCalls) ? toolCalls.length : 0);
    }, 0);
  };

  return (
    <div className="w-full h-full flex flex-col border-r border-gray-200 bg-white">
      {/* Header */}
      <div className="p-3 border-b border-gray-200 bg-gray-50 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <h2 className="text-sm font-semibold text-gray-800">Sessions</h2>
          {loading && <Loader2 className="h-4 w-4 animate-spin text-gray-400" />}
        </div>
        <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
          <span>{sessionStats.totalSessions} total</span>
          <span className="text-green-600">
            {sessionStats.completedSessions} completed
          </span>
          {sessionStats.errorSessions > 0 && (
            <span className="text-red-600">
              {sessionStats.errorSessions} with errors
            </span>
          )}
        </div>
      </div>

      {/* Session List */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        <div className="p-1">
        {sessionGroups.length === 0 && !loading ? (
          <div className="p-6 text-center text-gray-500 text-sm">
            {error ? 'Failed to load sessions' : 'No sessions found'}
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {sessionGroups.map((session) => {
              const isSelected = selectedSession?.sessionId === session.sessionId;
              const toolCallsCount = getToolCallCount(session.traces);
              
              return (
                <div
                  key={session.sessionId}
                  className={`p-3 mx-1 my-0.5 rounded-md hover:bg-gray-50 cursor-pointer transition-colors ${
                    isSelected ? 'bg-blue-50 ring-1 ring-blue-200' : 'hover:ring-1 hover:ring-gray-100'
                  }`}
                  onClick={() => onSelectSession(session)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(session.status)}
                        <span className="text-sm font-medium text-gray-900 truncate">
                          {session.sessionId.substring(0, 8)}...{session.sessionId.slice(-4)}
                        </span>
                      </div>
                      
                      <div className="mt-1.5 flex flex-wrap items-center gap-1.5">
                        {session.agentTypes.slice(0, 2).map((type) => (
                          <span
                            key={type}
                            className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700"
                          >
                            {type}
                          </span>
                        ))}
                        {session.agentTypes.length > 2 && (
                          <span className="text-xs text-gray-500">+{session.agentTypes.length - 2} more</span>
                        )}
                      </div>
                      
                      <div className="mt-2 flex items-center space-x-3 text-xs text-gray-500">
                        <div className="flex items-center">
                          <Clock className="h-3.5 w-3.5 mr-1 text-gray-400" />
                          <span title={new Date(session.latestTimestamp).toLocaleString()}>
                            {formatDistanceToNow(new Date(session.latestTimestamp))} ago
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          <MessageSquare className="h-3.5 w-3.5 text-gray-400" />
                          <span>{session.traces.length} trace{session.traces.length !== 1 ? 's' : ''}</span>
                        </div>
                        
                        {toolCallsCount > 0 && (
                          <div className="flex items-center space-x-1">
                            <Wrench className="h-3.5 w-3.5 text-gray-400" />
                            <span>{toolCallsCount} tool call{toolCallsCount !== 1 ? 's' : ''}</span>
                          </div>
                        )}
                      </div>
                      
                      {(session.errorCount > 0 || session.status === 'error') && (
                        <div className="mt-1.5">
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-50 text-red-700">
                            <AlertCircle className="h-3.5 w-3.5 mr-1" />
                            {session.errorCount > 0 ? `${session.errorCount} error${session.errorCount !== 1 ? 's' : ''}` : 'Error'}
                          </span>
                        </div>
                      )}
                    </div>
                    
                    <ChevronRight className="h-5 w-5 text-gray-400 flex-shrink-0 ml-2" />
                  </div>
                </div>
              );
            })}
          </div>
        )}
        
          {nextToken && !loading && (
            <div className="p-3 border-t border-gray-100 sticky bottom-0 bg-white">
              <Button
                onClick={onLoadMore}
                disabled={loadingMore}
                variant="ghost"
                size="sm"
                className="w-full text-sm text-gray-600 hover:bg-gray-100"
              >
                {loadingMore ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading...
                  </>
                ) : (
                  'Load more sessions'
                )}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
