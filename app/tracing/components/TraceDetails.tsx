import React, { useState } from "react";
import { Session<PERSON>roup, Trace, ToolCall, AIMessage } from "../types";
import { formatDate, getStatusColor, copyToClipboard } from "../utils";
import { ChevronRight, MessageSquare, Wrench, Copy, Check } from "lucide-react";

interface TraceDetailsProps {
	selectedSession: SessionGroup | null;
}

const ToolCallCard: React.FC<{ toolCall: ToolCall }> = ({ toolCall }) => {
	const [copied, setCopied] = useState(false);

	const handleCopy = () => {
		const args =
			typeof toolCall.arguments === "string"
				? toolCall.arguments
				: JSON.stringify(toolCall.arguments, null, 2);
		copyToClipboard(args);
		setCopied(true);
		setTimeout(() => setCopied(false), 2000);
	};

	return (
		<div className="mb-4 border-l-4 border-blue-200 pl-3">
			<div className="flex items-center justify-between text-sm font-medium text-gray-700 mb-1">
				<div className="flex items-center">
					<Wrench className="h-3.5 w-3.5 mr-1.5 text-blue-500" />
					{toolCall.function?.name || toolCall.type || "Tool Call"}
				</div>
				<button
					onClick={handleCopy}
					className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
					title="Copy arguments"
				>
					{copied ? (
						<Check className="h-3 w-3 mr-1" />
					) : (
						<Copy className="h-3 w-3 mr-1" />
					)}
					{copied ? "Copied!" : "Copy"}
				</button>
			</div>
			<div className="bg-blue-50 p-2 rounded text-xs font-mono overflow-x-auto">
				<pre className="whitespace-pre-wrap break-words">
					{typeof toolCall.arguments === "string"
						? toolCall.arguments
						: JSON.stringify(toolCall.arguments, null, 2)}
				</pre>
			</div>
		</div>
	);
};

const AIResponseCard: React.FC<{ response: AIMessage }> = ({ response }) => {
	const [copied, setCopied] = useState(false);

	// Debug: Log the response to help with troubleshooting
	console.log("AI Response Data:", response);

	const handleCopy = () => {
		let content = "";

		if (response.content) {
			if (Array.isArray(response.content)) {
				content = response.content
					.map((item) => {
						if (typeof item === "string") return item;
						if (typeof item === "object" && item && "text" in item) {
							const textItem = item as { text: { value: string } | string };
							return typeof textItem.text === "string"
								? textItem.text
								: textItem.text.value;
						}
						return JSON.stringify(item, null, 2);
					})
					.join("\n\n");
			} else if (typeof response.content === "object") {
				content = JSON.stringify(response.content, null, 2);
			} else {
				content = String(response.content);
			}
		}

		copyToClipboard(content);
		setCopied(true);
		setTimeout(() => setCopied(false), 2000);
	};

	const renderContent = () => {
		if (!response.content) {
			return <div className="text-gray-500 italic">No content available</div>;
		}
		// A simple, safe way to render content of unknown structure
		return (
			<pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto whitespace-pre-wrap">
				{JSON.stringify(response.content, null, 2)}
			</pre>
		);
	};

	return (
		<div className="mt-4 border-l-4 border-green-200 pl-3">
			<div className="flex items-center justify-between text-sm font-medium text-gray-700 mb-1">
				<div className="flex items-center">
					<MessageSquare className="h-3.5 w-3.5 mr-1.5 text-green-500" />
					AI Response {response.role ? `(${response.role})` : ""}
				</div>
				<button
					onClick={handleCopy}
					className="text-xs text-green-600 hover:text-green-800 flex items-center"
					title="Copy response"
				>
					{copied ? (
						<Check className="h-3 w-3 mr-1" />
					) : (
						<Copy className="h-3 w-3 mr-1" />
					)}
					{copied ? "Copied!" : "Copy"}
				</button>
			</div>
			<div className="bg-green-50 p-3 rounded text-sm text-gray-800">
				{renderContent()}
			</div>
		</div>
	);
};

// Type guard for AIMessage
const isAIMessage = (obj: unknown): obj is AIMessage => {
	return (
		obj !== null &&
		typeof obj === "object" &&
		("content" in obj || "role" in obj)
	);
};

// Type guard for ToolCall array
const isToolCallArray = (arr: unknown): arr is ToolCall[] => {
	return (
		Array.isArray(arr) &&
		(arr.length === 0 ||
			arr.every(
				(item) =>
					item && typeof item === "object" && "type" in item && "id" in item
			))
	);
};

const TraceCard: React.FC<{ trace: Trace }> = ({ trace }) => {
	// Debug: Log the trace to help with troubleshooting
	console.log("Trace Data:", trace);

	// Extract and validate data with proper typing - handle both old and new field names
	const rawAiResponse = trace.aiResponse || trace.traceData?.ai_response;
	const aiResponse = isAIMessage(rawAiResponse) ? rawAiResponse : null;

	const rawToolCalls = trace.toolCalls || trace.traceData?.tool_calls || [];
	const toolCalls: ToolCall[] = isToolCallArray(rawToolCalls)
		? rawToolCalls
		: [];

	const userMessage = trace.userMessage || trace.traceData?.user_message;

	// Debug: Log the extracted data
	console.log("Extracted AI Response:", aiResponse);
	console.log("Extracted Tool Calls:", toolCalls);
	console.log("Extracted User Message:", userMessage);

	const hasAIResponse = !!aiResponse;
	const hasUserMessage = !!userMessage;
	const hasToolCalls = toolCalls.length > 0;

	return (
		<div className="border border-gray-200 rounded-lg overflow-hidden mb-6">
			<div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
				<div className="flex justify-between items-start">
					<div>
						<h3 className="text-sm font-medium text-gray-900">
							{trace.agent_type || "Unknown Agent"}
						</h3>
						<p className="text-xs text-gray-500 mt-1">
							{formatDate(trace.createdAt || trace.timestamp)}
							{trace.duration_seconds !== undefined && (
								<span className="ml-2">
									• {trace.duration_seconds.toFixed(2)}s
								</span>
							)}
						</p>
					</div>
					<div className="flex items-center space-x-2">
						<span
							className={`px-2 py-1 text-xs rounded-full ${getStatusColor(
								trace.status
							)}`}
						>
							{trace.status || "completed"}
						</span>
					</div>
				</div>

				{/* Metadata */}
				{trace.metadata && Object.keys(trace.metadata).length > 0 && (
					<div className="mt-2 pt-2 border-t border-gray-100">
						<div className="grid grid-cols-2 gap-x-4 gap-y-1 text-xs">
							{Object.entries(trace.metadata).map(([key, value]) => (
								<div key={key} className="truncate">
									<span className="font-medium text-gray-600">{key}:</span>{" "}
									<span className="text-gray-500">
										{typeof value === "string" || typeof value === "number"
											? value
											: JSON.stringify(value)}
									</span>
								</div>
							))}
						</div>
					</div>
				)}
			</div>

			<div className="p-4">
				{/* User Message */}
				{hasUserMessage && (
					<div className="p-4 bg-gray-50 border-b border-gray-200">
						<div className="flex items-center text-sm font-medium text-gray-700 mb-2">
							<MessageSquare className="h-4 w-4 mr-1.5 text-gray-500" />
							User Message
						</div>
						<div className="text-sm text-gray-800 whitespace-pre-wrap">
							{typeof userMessage === "string"
								? userMessage
								: JSON.stringify(userMessage, null, 2)}
						</div>
					</div>
				)}

				{/* @ts-expect-error - The toolCalls array is being used without proper typing, but it's needed for the tool call functionality to work correctly.   */}
				{hasToolCalls && toolCalls.length > 0 && (
					<div className="p-4 border-b border-gray-200">
						<div className="flex items-center text-sm font-medium text-gray-700 mb-2">
							<Wrench className="h-4 w-4 mr-1.5 text-blue-500" />
							Tool Calls ({toolCalls.length})
						</div>
						{toolCalls.map((toolCall, index) => (
							<ToolCallCard key={index} toolCall={toolCall} />
						))}
					</div>
				)}

				{/* AI Response */}
				{hasAIResponse && aiResponse && (
					<div className="p-4">
						<AIResponseCard response={aiResponse} />
					</div>
				)}

				{/* Error */}
				{trace.error && (
					<div className="mt-4 p-3 bg-red-50 border-l-4 border-red-400 rounded-r">
						<h4 className="text-sm font-medium text-red-800 mb-1">Error</h4>
						<pre className="text-xs text-red-700 whitespace-pre-wrap font-mono">
							{(() => {
								if (typeof trace.error === "string") {
									return trace.error;
								}
								if (trace.error instanceof Error) {
									return trace.error.message;
								}
								return JSON.stringify(trace.error, null, 2);
							})()}
						</pre>
					</div>
				)}

				{/* Metadata */}
				{(trace.metadata || trace.traceData?.metadata) && (
					<div className="mt-4 pt-3 border-t border-gray-100">
						<div className="flex justify-between items-center mb-2">
							<h4 className="text-xs font-medium text-gray-500">Metadata</h4>
							<button
								onClick={() =>
									copyToClipboard(
										JSON.stringify(
											trace.metadata || trace.traceData?.metadata || {},
											null,
											2
										)
									)
								}
								className="text-xs text-blue-600 hover:text-blue-800 flex items-center space-x-1"
								title="Copy to clipboard"
							>
								<svg
									className="h-3.5 w-3.5"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
									/>
								</svg>
								<span>Copy</span>
							</button>
						</div>
						<div className="bg-gray-50 rounded p-2 max-h-40 overflow-auto">
							<pre className="text-xs font-mono">
								{JSON.stringify(
									trace.metadata || trace.traceData?.metadata || {},
									null,
									2
								)}
							</pre>
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

export const TraceDetails: React.FC<TraceDetailsProps> = ({
	selectedSession,
}) => {
	if (!selectedSession) {
		return (
			<div className="flex-1 flex items-center justify-center text-gray-500">
				<div className="text-center">
					<ChevronRight className="h-12 w-12 mx-auto text-gray-300 mb-4" />
					<p>Select a session to view trace details</p>
				</div>
			</div>
		);
	}

	return (
		<div className="flex-1 flex flex-col min-h-0">
			<div className="flex-shrink-0 p-4 border-b border-gray-200 bg-white">
				<h2 className="text-sm font-medium text-gray-900">Session Details</h2>
				<div className="mt-1 flex flex-wrap items-center gap-x-4 gap-y-1">
					<p
						className="text-xs text-gray-500 truncate max-w-xs"
						title={selectedSession.sessionId}
					>
						<span className="font-medium">ID:</span> {selectedSession.sessionId}
					</p>
					<p className="text-xs text-gray-500">
						<span className="font-medium">Traces:</span>{" "}
						{selectedSession.traces.length}
					</p>
					<p className="text-xs text-gray-500">
						<span className="font-medium">Status:</span>{" "}
						{selectedSession.status}
					</p>
					<p className="text-xs text-gray-500">
						<span className="font-medium">Last updated:</span>{" "}
						{new Date(selectedSession.latestTimestamp).toLocaleString()}
					</p>
				</div>
			</div>
			<div className="flex-1 overflow-y-auto overflow-x-hidden">
				<div className="p-4 space-y-6">
					{selectedSession.traces.map((trace) => (
						<TraceCard key={trace.id} trace={trace} />
					))}
					{selectedSession.traces.length === 0 && (
						<div className="text-center text-gray-500 py-8">
							<p>No trace data available for this session</p>
						</div>
					)}
				</div>
			</div>
		</div>
	);
};
