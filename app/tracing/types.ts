export interface ToolCall {
	id: string;
	type: string;
	name?: string;
	function?: {
		name: string;
		arguments: string;
	};
	arguments?: string | Record<string, unknown>;
}

export interface AIMessage {
	id?: string;
	content: string | unknown[];
	role: string;
	name?: string;
	tool_calls?: ToolCall[];
}

export interface TraceData {
	tool_calls?: ToolCall[];
	ai_response?: AIMessage;
	status?: string;
	agent_type?: string;
	metadata?: Record<string, unknown>;
	error?: unknown;
	duration_seconds?: number;
	user_message?: string;
}

export interface Trace {
	id: string;
	sessionId: string; // Maps to session_id from backend
	timestamp: string;
	createdAt?: string; // Maps to created_at from backend
	agent_type: string;
	status: string;
	duration_seconds?: number;
	error?: unknown;
	metadata: Record<string, unknown>;
	traceData?: TraceData;
	userMessage?: string; // Maps to user_message from backend
	toolCalls?: ToolCall[]; // Maps to tool_calls from backend
	aiResponse?: AIMessage; // Maps to ai_response from backend
	// New Supabase fields
	user_id?: string;
	start_time?: string;
	end_time?: string;
	updated_at?: string;
}

export interface SessionGroup {
	sessionId: string;
	traces: Trace[];
	agentTypes: string[];
	status: string;
	latestTimestamp: string;
	errorCount: number;
}

export interface SupabaseTrace {
	id: string;
	session_id: string;
	user_id: string | null;
	agent_type: string;
	status: string;
	user_message: string | null;
	ai_response: AIMessage | null;
	tool_calls: ToolCall[] | null;
	trace_data: TraceData | null;
	metadata: Record<string, unknown> | null;
	duration_seconds: number | null;
	start_time: string | null;
	end_time: string | null;
	created_at: string;
	updated_at: string;
}
