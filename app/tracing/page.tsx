'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useTraces } from './hooks/useTraces';
import { Header } from './components/Header';
import { SessionList } from './components/SessionList';
import { TraceDetails } from './components/TraceDetails';
import { SessionGroup } from './types';

export default function TracingPage() {
  const [sessionIdFilter, setSessionIdFilter] = useState('');
  const [debouncedFilter, setDebouncedFilter] = useState('');
  const {
    traces,
    sessionGroups,
    loading,
    loadingMore,
    error,
    nextToken,
    lastRefreshed,
    handleRefresh,
    handleLoadMore,
    fetchTraces,
  } = useTraces(debouncedFilter);

  const [selectedSession, setSelectedSession] = useState<SessionGroup | null>(null);

  useEffect(() => {
    if (sessionGroups.length > 0 && !selectedSession) {
      setSelectedSession(sessionGroups[0]);
    }
  }, [sessionGroups, selectedSession]);

  const handleFilterChange = useCallback((value: string) => {
    setSessionIdFilter(value);
  }, []);

  const handleFilterSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    setDebouncedFilter(sessionIdFilter);
  }, [sessionIdFilter]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedFilter(sessionIdFilter);
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [sessionIdFilter]);

  if (loading && traces.length === 0 && !error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-500 mx-auto" />
          <p className="mt-4 text-gray-600">Loading tracing data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen w-full bg-white">
      <div className="flex-shrink-0">
        <Header
          loading={loading}
          tracesCount={traces.length}
          lastRefreshed={lastRefreshed}
          sessionIdFilter={sessionIdFilter}
          onFilterChange={handleFilterChange}
          onFilterSubmit={handleFilterSubmit}
          onRefresh={handleRefresh}
        />

        {error && (
          <div className="p-4 bg-red-50 border-b border-red-200">
            <div className="flex items-center justify-between">
              <div className="text-red-700">
                <strong>Error:</strong> {error}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchTraces(false)}
                disabled={loading}
              >
                Retry
              </Button>
            </div>
          </div>
        )}
      </div>

      <div className="flex-1 min-h-0">
        <div className="h-full flex overflow-hidden">
          <SessionList
            sessionGroups={sessionGroups}
            selectedSession={selectedSession}
            onSelectSession={setSelectedSession}
            loading={loading}
            error={error}
            nextToken={nextToken}
            loadingMore={loadingMore}
            onLoadMore={handleLoadMore}
          />
          <TraceDetails selectedSession={selectedSession} />
        </div>
      </div>
    </div>
  );
}
