import { format, formatDistanceToNow as dateFnsFormatDistanceToNow } from 'date-fns';

export const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), 'MMM d, yyyy, h:mm:ss a');
  } catch (e) {
    console.warn('Date formatting error:', e);
    return dateString;
  }
};

export const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'error':
      return 'bg-red-100 text-red-800';
    case 'started':
      return 'bg-blue-100 text-blue-800';
    case 'processing':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    // You could add a toast notification here
  } catch (err) {
    console.error('Failed to copy to clipboard:', err);
  }
};

export const formatDistanceToNow = (date: Date | string | number) => {
  try {
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    return dateFnsFormatDistanceToNow(dateObj, { addSuffix: true })
      .replace('about ', '')
      .replace('less than a minute', 'just now')
      .replace('minute', 'min');
  } catch (e) {
    console.warn('Date formatting error:', e);
    return '';
  }
};
