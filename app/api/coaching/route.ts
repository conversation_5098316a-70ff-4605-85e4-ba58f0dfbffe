import { createClient } from '@/lib/utils/supabase/server'
import { NextResponse } from 'next/server'

export async function GET(_request: Request) {
  const supabase = await createClient()
  
  try {
    const { data, error } = await supabase
      .from('coaching_instructions')
      .select('*')
      .order('priority', { ascending: true })

    if (error) throw error
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching coaching instructions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch coaching instructions' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  const supabase = await createClient()
  
  try {
    const instruction = await request.json()
    const { data, error } = await supabase
      .from('coaching_instructions')
      .insert(instruction)
      .select()
      .single()

    if (error) throw error
    return NextResponse.json(data, { status: 201 })
  } catch (error) {
    console.error('Error creating coaching instruction:', error)
    return NextResponse.json(
      { error: 'Failed to create coaching instruction' },
      { status: 500 }
    )
  }
}
