import { createClient } from "@/lib/utils/supabase/server";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export const PATCH = async (request: NextRequest) => {
	const supabase = await createClient();
	const id = new URL(request.url).searchParams.get("id");
	try {
		const updates = await request.json();

		const { data, error } = await supabase
			.from("coaching_instructions")
			.update(updates)
			.eq("id", id)
			.select()
			.single();

		if (error) throw error;

		return NextResponse.json(data);
	} catch (error) {
		console.error("Error updating coaching instruction:", error);
		return NextResponse.json(
			{ error: "Failed to update coaching instruction" },
			{ status: 500 }
		);
	}
};

export const DELETE = async (request: NextRequest) => {
	const supabase = await createClient();
	const id = new URL(request.url).searchParams.get("id");
	try {
		const { error } = await supabase
			.from("coaching_instructions")
			.delete()
			.eq("id", id);

		if (error) throw error;

		return new Response(null, { status: 204 });
	} catch (error) {
		console.error("Error deleting coaching instruction:", error);
		return NextResponse.json(
			{ error: "Failed to delete coaching instruction" },
			{ status: 500 }
		);
	}
};
