import { NextRequest, NextResponse } from "next/server";
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
	try {
		const cookieStore = await cookies();

		const supabase = createServerClient(
			process.env.NEXT_PUBLIC_SUPABASE_URL!,
			process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
			{
				cookies: {
					getAll() {
						return cookieStore.getAll();
					},
					setAll(cookiesToSet) {
						try {
							cookiesToSet.forEach(({ name, value, options }) =>
								cookieStore.set(name, value, options)
							);
						} catch {
							// The `setAll` method was called from a Server Component.
							// This can be ignored if you have middleware refreshing
							// user sessions.
						}
					},
				},
			}
		);

		// Get the current authenticated user (more secure than getSession)
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Get query parameters
		const { searchParams } = new URL(request.url);
		const sessionId = searchParams.get("session_id");
		const limit = parseInt(searchParams.get("limit") || "100");
		const offset = parseInt(searchParams.get("offset") || "0");

		// Build the query
		let query = supabase
			.from("traces")
			.select("*")
			.eq("user_id", user.id) // Filter by authenticated user
			.order("created_at", { ascending: false })
			.range(offset, offset + limit - 1);

		// Add session filter if provided
		if (sessionId) {
			query = query.eq("session_id", sessionId);
		}

		const { data: traces, error } = await query;

		if (error) {
			console.error("Error fetching traces from Supabase:", error);
			return NextResponse.json(
				{ error: "Failed to fetch traces" },
				{ status: 500 }
			);
		}

		// Check if there are more traces (simple pagination)
		const hasMore = traces && traces.length === limit;
		const nextToken = hasMore ? String(offset + limit) : null;

		return NextResponse.json({
			traces: traces || [],
			next_token: nextToken,
		});
	} catch (error) {
		console.error("Error in tracing API:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 }
		);
	}
}
