import { NextResponse } from 'next/server';
import { OpenAI } from 'openai';

export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
  try {
    const { query } = await request.json() as { query?: string };
    
    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      );
    }

    // For Azure OpenAI, we need to use the deployment name as the model parameter
    const deploymentName = process.env.AZURE_OPENAI_DEPLOYMENT_NAME || 'gpt-5-nano';
    
    const openai = new OpenAI({
      apiKey: process.env.GPT_5_KEY,
      baseURL: `${process.env.GPT_5_URL}/openai/deployments/${deploymentName}`,
      defaultQuery: { 'api-version': '2025-01-01-preview' },
      defaultHeaders: { 'api-key': process.env.GPT_5_KEY },
    });

    const response = await openai.chat.completions.create({
      model: deploymentName,
      messages: [
        {
          role: 'system',
          content: `You are an AI assistant for a hotel management system. Your goal is to provide helpful, relevant search suggestions based on the user's input. The suggestions should be concise and directly related to hotel management tasks.

CRITICAL INSTRUCTIONS:
1.  **Analyze the user's query**: Understand the user's intent, whether they are asking about room availability, pricing, updates, or general hotel information.
2.  **Provide relevant suggestions**: Based on the query, offer 3-5 suggestions that would logically follow or refine their request.
3.  **Format the output**: Return a JSON array of strings, with each string being a search suggestion. Do not include any other text or formatting.

<Example>
User query: "show me rooms"
["For which hotel?", "What room type are you interested in?", "Can you provide the hotel name and room type?"]
</Example>
<Example>
User query: "update price"
["For which hotel?", "What is the room type and new price?", "I can update the price if you provide the hotel, room type, and new price."]
</Example>
<Example>
User query: "what hotels are in"
["What location are you looking for?", "I can list all hotels if you'd like.", "Tell me the city or area to search for hotels."]
</Example>
`
        },
        {
          role: 'user',
          content: `Provide search suggestions for: ${query}`
        }
      ],
      temperature: 1,
      max_completion_tokens: 1024,
    });

    // Log the full response for debugging
    console.log('OpenAI API Response:', JSON.stringify(response, null, 2));
    
    // Extract the content from the response
    const content = response.choices[0]?.message?.content || '';
    console.log('Extracted content:', content);
    
    let suggestions: string[] = [];
    
    // Try to parse the content as JSON, fallback to text parsing if needed
    try {
      // Clean up the content to ensure it's valid JSON
      const cleanContent = content.replace(/^```(json\n)?|```$/g, '').trim();
      const parsed = JSON.parse(cleanContent);
      suggestions = Array.isArray(parsed) ? parsed : [];
      console.log('Parsed suggestions:', suggestions);
    } catch (e) {
      console.error('Error parsing suggestions:', e);
      // If JSON parsing fails, try to extract suggestions from plain text
      const lines = content.split('\n');
      suggestions = lines
        .map((line: string) => line.trim().replace(/^[\d\s.-]+/g, '').trim())
        .filter((line: string) => line.length > 0 && !line.startsWith('```'))
        .slice(0, 5);
      console.log('Fallback suggestions:', suggestions);
    }

    return NextResponse.json({ suggestions });
  } catch (error) {
    console.error('Error generating suggestions:', error);
    return NextResponse.json(
      { error: 'Failed to generate suggestions' },
      { status: 500 }
    );
  }
}
